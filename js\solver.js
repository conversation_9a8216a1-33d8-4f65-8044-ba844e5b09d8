/**
 * Clean Solver Integration
 * Handles communication with Kociemba solver backend
 */

class Solver {
    constructor() {
        this.serverUrl = 'http://localhost:5000';
        this.checkServerStatus();
    }

    async checkServerStatus() {
        try {
            const response = await fetch(`${this.serverUrl}/health`);
            if (response.ok) {
                console.log('Solver server is running');
                return true;
            }
        } catch (error) {
            console.warn('Solver server not available:', error.message);
        }
        return false;
    }

    async solve(cubeString) {
        try {
            console.log('Sending cube to solver:', cubeString);
            
            const response = await fetch(`${this.serverUrl}/solve`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    cube_string: cubeString 
                })
            });

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const data = await response.json();
            
            if (data.error) {
                throw new Error(data.error);
            }

            console.log('Solution received:', data.solution);
            return data.solution;
            
        } catch (error) {
            console.error('Solver error:', error);
            throw error;
        }
    }

    async validate(cubeString) {
        try {
            const response = await fetch(`${this.serverUrl}/validate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ 
                    cube_string: cubeString 
                })
            });

            if (!response.ok) {
                throw new Error(`Server responded with status: ${response.status}`);
            }

            const data = await response.json();
            return data.valid;
            
        } catch (error) {
            console.error('Validation error:', error);
            return false;
        }
    }
}

// Export for use in other modules
window.Solver = Solver;
