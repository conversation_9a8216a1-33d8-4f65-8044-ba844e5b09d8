/**
 * Clean 2D Cube Visualization
 * Handles flat cube display independent of visual orientation
 */

class Cube2D {
    constructor(container, cubeState) {
        this.container = container;
        this.cubeState = cubeState;
        this.init2D();
    }

    init2D() {
        this.render();
    }

    render() {
        // Clear container
        this.container.innerHTML = '';

        const size = this.cubeState.size;
        const faces = this.cubeState.stringToFaces();

        // Create 2D layout: 
        //     U
        //   L F R
        //     D
        //     B

        const layout = [
            [null, 'U', null],
            ['L', 'F', 'R'], 
            [null, 'D', null],
            [null, 'B', null]
        ];

        layout.forEach((row, rowIndex) => {
            row.forEach((faceName, colIndex) => {
                if (faceName) {
                    const faceElement = this.createFaceElement(faces[faceName], faceName, size);
                    faceElement.style.gridColumn = colIndex + 1;
                    faceElement.style.gridRow = rowIndex + 1;
                    this.container.appendChild(faceElement);
                }
            });
        });
    }

    createFaceElement(faceArray, faceName, size) {
        const faceDiv = document.createElement('div');
        faceDiv.className = `face face-${size} face-${faceName}`;

        // Create stickers
        for (let row = 0; row < size; row++) {
            for (let col = 0; col < size; col++) {
                const sticker = document.createElement('div');
                sticker.className = `sticker color-${faceArray[row][col]}`;
                
                // Add click handler for manual color changing (optional)
                sticker.addEventListener('click', () => {
                    this.handleStickerClick(faceName, row, col);
                });
                
                faceDiv.appendChild(sticker);
            }
        }

        return faceDiv;
    }

    handleStickerClick(face, row, col) {
        // Optional: Allow manual color changing for debugging
        console.log(`Clicked sticker: ${face}[${row}][${col}]`);
        
        // Cycle through colors for testing
        const colors = ['white', 'yellow', 'red', 'orange', 'green', 'blue'];
        const faces = this.cubeState.stringToFaces();
        const currentColor = faces[face][row][col];
        const currentIndex = colors.indexOf(currentColor);
        const nextIndex = (currentIndex + 1) % colors.length;
        const nextColor = colors[nextIndex];
        
        // Update cube state
        faces[face][row][col] = nextColor;
        this.cubeState.cubeString = this.cubeState.facesToString(faces);
        
        // Refresh display
        this.refresh();
        
        // Notify other components
        if (window.app && window.app.onStateChange) {
            window.app.onStateChange();
        }
    }

    // Update 2D display when cube state changes
    refresh() {
        this.render();
    }

    // Handle size changes
    changeSize() {
        this.render();
    }
}

// Export for use in other modules
window.Cube2D = Cube2D;
