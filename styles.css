/* Clean Rubik's Cube Solver Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #1a1a1a, #2d2d2d);
    color: white;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    text-align: center;
    margin-bottom: 30px;
}

header h1 {
    font-size: 2.5em;
    margin-bottom: 20px;
    color: #4CAF50;
}

.cube-size-selector {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.size-btn {
    padding: 10px 20px;
    background: #333;
    color: white;
    border: 2px solid #555;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.size-btn:hover {
    background: #4CAF50;
    border-color: #4CAF50;
}

.size-btn.active {
    background: #4CAF50;
    border-color: #4CAF50;
}

.cube-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    margin-bottom: 30px;
}

.view-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.view-section h3 {
    margin-bottom: 15px;
    color: #4CAF50;
}

#cube-canvas {
    border: 2px solid #555;
    border-radius: 5px;
    background: #000;
}

.orientation-controls {
    margin-top: 15px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.orientation-btn {
    padding: 8px 15px;
    background: #2196F3;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.orientation-btn:hover {
    background: #1976D2;
}

/* 2D Flat Cube Styles */
.flat-cube {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: repeat(4, 1fr);
    gap: 5px;
    max-width: 300px;
    margin: 0 auto;
}

.face {
    display: grid;
    gap: 1px;
    padding: 4px;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 4px;
    background: rgba(0, 0, 0, 0.2);
}

.face-2 { grid-template-columns: repeat(2, 1fr); }
.face-3 { grid-template-columns: repeat(3, 1fr); }
.face-4 { grid-template-columns: repeat(4, 1fr); }

.sticker {
    width: 20px;
    height: 20px;
    border: 1px solid rgba(0, 0, 0, 0.3);
    border-radius: 2px;
    transition: all 0.2s ease;
}

/* Face positioning in 2D layout */
.face-U { grid-column: 2; grid-row: 1; }
.face-L { grid-column: 1; grid-row: 2; }
.face-F { grid-column: 2; grid-row: 2; }
.face-R { grid-column: 3; grid-row: 2; }
.face-D { grid-column: 2; grid-row: 3; }
.face-B { grid-column: 2; grid-row: 4; }

/* Color classes */
.color-white { background-color: #ffffff; }
.color-yellow { background-color: #ffff00; }
.color-red { background-color: #ff0000; }
.color-orange { background-color: #ff8000; }
.color-green { background-color: #00ff00; }
.color-blue { background-color: #0000ff; }
.color-gray { background-color: #808080; }

.controls-section {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px;
    border-radius: 10px;
    margin-bottom: 20px;
    text-align: center;
}

.controls-section h3 {
    margin-bottom: 15px;
    color: #4CAF50;
}

.face-controls {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
}

.face-row {
    display: flex;
    gap: 10px;
}

.face-btn {
    padding: 10px 15px;
    background: #FF9800;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    min-width: 50px;
}

.face-btn:hover {
    background: #F57C00;
    transform: scale(1.05);
}

.action-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-bottom: 20px;
}

.action-controls button {
    padding: 12px 20px;
    background: #9C27B0;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
}

.action-controls button:hover {
    background: #7B1FA2;
    transform: translateY(-2px);
}

.status-section {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.cube-state, .solution-display {
    background: rgba(255, 255, 255, 0.1);
    padding: 15px;
    border-radius: 10px;
}

.cube-state h4, .solution-display h4 {
    margin-bottom: 10px;
    color: #4CAF50;
}

#cube-string-display {
    font-family: monospace;
    font-size: 12px;
    word-break: break-all;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 5px;
    max-height: 100px;
    overflow-y: auto;
}

#solution-moves {
    font-family: monospace;
    background: rgba(0, 0, 0, 0.3);
    padding: 10px;
    border-radius: 5px;
    min-height: 50px;
}

/* Info Panel */
.info-panel {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-panel h4 {
    margin: 0 0 15px 0;
    color: #4CAF50;
    font-size: 16px;
    font-weight: 600;
}

/* Color Mappings */
.color-mappings {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 8px;
    margin-bottom: 15px;
}

.mapping-item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.face-label {
    font-weight: 600;
    font-family: 'Courier New', monospace;
    min-width: 60px;
    color: #E8F5E8;
}

.color-box {
    width: 20px;
    height: 20px;
    border-radius: 3px;
    border: 2px solid #333;
    display: inline-block;
}

.color-name {
    font-size: 14px;
    color: #B0BEC5;
}

.note {
    padding: 12px;
    background: rgba(33, 150, 243, 0.1);
    border-left: 4px solid #2196F3;
    border-radius: 6px;
    color: #E3F2FD;
    line-height: 1.4;
}

.note strong {
    color: #BBDEFB;
}

/* Mouse Controls Info */
.mouse-controls-info {
    margin-top: 15px;
}

.control-item {
    padding: 6px 0;
    color: #B0BEC5;
    font-size: 13px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.control-item:last-child {
    border-bottom: none;
}

.control-item strong {
    color: #E8F5E8;
    font-weight: 600;
}

/* Manual Scramble Input */
.manual-scramble {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.manual-scramble h4 {
    margin: 0 0 15px 0;
    color: #4CAF50;
    font-size: 16px;
    font-weight: 600;
}

.scramble-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

#manual-scramble-input {
    flex: 1;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    color: #E8F5E8;
    font-size: 14px;
    font-family: 'Courier New', monospace;
}

#manual-scramble-input::placeholder {
    color: #B0BEC5;
}

#manual-scramble-input:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.15);
}

#apply-manual-scramble {
    padding: 10px 20px;
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.3s ease;
    white-space: nowrap;
}

#apply-manual-scramble:hover {
    background: #45a049;
    transform: translateY(-2px);
}

.scramble-help {
    color: #B0BEC5;
    font-size: 12px;
    line-height: 1.4;
}

/* Wide Move Controls */
.wide-controls {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.wide-controls h4 {
    margin: 0 0 15px 0;
    color: #FF9800;
    font-size: 16px;
    font-weight: 600;
}

.wide-face-controls {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.wide-btn {
    padding: 8px 12px;
    background: #FF9800;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-weight: bold;
    font-size: 14px;
    transition: all 0.3s ease;
    min-width: 50px;
}

.wide-btn:hover {
    background: #F57C00;
    transform: translateY(-2px);
}

.wide-btn:active {
    transform: translateY(0);
}

@media (max-width: 768px) {
    .cube-container {
        grid-template-columns: 1fr;
    }

    .status-section {
        grid-template-columns: 1fr;
    }

    .action-controls {
        flex-wrap: wrap;
    }

    .color-mappings {
        grid-template-columns: 1fr;
    }
}
