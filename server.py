#!/usr/bin/env python3
"""
Clean Rubik's Cube Solver Server
Flask API server for Kociemba algorithm integration
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import kociemba
import re

# Try to import optional solvers
try:
    from cube_solver import solve_222
    CUBE_SOLVER_AVAILABLE = True
except ImportError:
    CUBE_SOLVER_AVAILABLE = False
    print("Warning: cube-solver not available for 2x2 cubes")

try:
    from rubik_solver import utils as rubik_utils
    RUBIK_SOLVER_AVAILABLE = True
except ImportError:
    RUBIK_SOLVER_AVAILABLE = False
    print("Warning: rubik-solver not available for 4x4 cubes")

app = Flask(__name__)
CORS(app)

def detect_cube_format(cube_string):
    """
    Detect if cube string is in original format (WYGBRO) or Kociemba format (URFDLB)
    """
    original_chars = set('WYGBRO')
    kociemba_chars = set('URFDLB')

    cube_chars = set(cube_string)

    if cube_chars.issubset(original_chars):
        return "original"
    elif cube_chars.issubset(kociemba_chars):
        return "kociemba"
    else:
        return "unknown"

def validate_cube_string(cube_string):
    """
    Validate cube string format and content
    Supports both original format (WYGBRO) and Kociemba format (URFDLB)
    """
    if not cube_string:
        return False, "Empty cube string"

    # Check length for different cube sizes
    valid_lengths = {
        24: "2x2",  # 6 faces * 4 stickers
        54: "3x3",  # 6 faces * 9 stickers
        96: "4x4"   # 6 faces * 16 stickers
    }

    length = len(cube_string)
    print(f"Validating cube string: {cube_string}")  # Debug print
    print(f"String length: {length}")  # Debug print

    if length not in valid_lengths:
        return False, f"Invalid cube string length: {length}. Expected 24 (2x2), 54 (3x3), or 96 (4x4)"

    # Detect format
    format_type = detect_cube_format(cube_string)
    print(f"Detected format: {format_type}")  # Debug print

    if format_type == "original":
        valid_colors = set('WYGBRO')  # White, Yellow, Green, Blue, Red, Orange
    elif format_type == "kociemba":
        valid_colors = set('URFDLB')  # Up, Right, Front, Down, Left, Back
    else:
        return False, f"Invalid characters in cube string. Expected either WYGBRO (original) or URFDLB (Kociemba) format"

    if not all(c in valid_colors for c in cube_string):
        invalid_chars = set(cube_string) - valid_colors
        return False, f"Invalid characters in cube string: {invalid_chars}"

    # For 3x3 cubes, check if it's a valid cube configuration
    if length == 54:
        # Count each color - should have exactly 9 of each
        color_counts = {}
        for color in cube_string:
            color_counts[color] = color_counts.get(color, 0) + 1

        expected_count = 9
        for color in valid_colors:
            if color_counts.get(color, 0) != expected_count:
                return False, f"Invalid color distribution. Color '{color}' appears {color_counts.get(color, 0)} times, expected {expected_count}"

    return True, f"Valid {format_type} format"

def convert_to_kociemba_format(cube_string):
    """
    Convert our cube string format to Kociemba format
    Our format: WYGBRO (White, Yellow, Green, Blue, Red, Orange)
    Kociemba format: URFDLB (Up, Right, Front, Down, Left, Back)
    
    Our mapping: U=W, D=Y, F=G, B=B, R=R, L=O
    """
    if len(cube_string) != 54:
        raise ValueError("Only 3x3 cubes supported for Kociemba algorithm")
    
    # Color mapping from our format to Kociemba
    color_map = {
        'W': 'U',  # White -> Up
        'Y': 'D',  # Yellow -> Down  
        'G': 'F',  # Green -> Front
        'B': 'B',  # Blue -> Back
        'R': 'R',  # Red -> Right
        'O': 'L'   # Orange -> Left
    }
    
    return ''.join(color_map[c] for c in cube_string)

def convert_from_kociemba_format(kociemba_string):
    """
    Convert Kociemba format back to our format
    """
    # Reverse mapping
    color_map = {
        'U': 'W',  # Up -> White
        'D': 'Y',  # Down -> Yellow
        'F': 'G',  # Front -> Green
        'B': 'B',  # Back -> Blue
        'R': 'R',  # Right -> Red
        'L': 'O'   # Left -> Orange
    }
    
    return ''.join(color_map[c] for c in kociemba_string)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'message': 'Clean Rubik\'s Cube Solver Server is running',
        'kociemba_available': True
    })

@app.route('/validate', methods=['POST'])
def validate_cube():
    """Validate cube string"""
    try:
        data = request.get_json()
        if not data or 'cube_string' not in data:
            return jsonify({'error': 'Missing cube_string parameter'}), 400
        
        cube_string = data['cube_string'].upper()
        is_valid, message = validate_cube_string(cube_string)
        
        return jsonify({
            'valid': is_valid,
            'message': message,
            'cube_string': cube_string
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/solve', methods=['POST'])
def solve_cube():
    """Solve cube using appropriate algorithm based on size"""
    try:
        data = request.get_json()
        print("Received data:", data)  # Debug print

        if not data:
            return jsonify({'error': 'No JSON data received'}), 400
        if 'cube_string' not in data:
            return jsonify({'error': 'Missing cube_string parameter in JSON data'}), 400

        cube_string = data['cube_string'].upper()
        cube_size = len(cube_string)
        print(f"Processing cube string (length {cube_size}):", cube_string)  # Debug print

        # Validate cube string
        is_valid, message = validate_cube_string(cube_string)
        print(f"Validation result: {is_valid}, Message: {message}")  # Debug print

        if not is_valid:
            return jsonify({
                'error': f'Invalid cube: {message}',
                'cube_string': cube_string,
                'validation_details': {
                    'length': len(cube_string),
                    'unique_chars': list(set(cube_string)),
                    'char_counts': {c: cube_string.count(c) for c in set(cube_string)}
                }
            }), 400

        # Convert to original format if needed
        format_type = detect_cube_format(cube_string)
        if format_type == "kociemba":
            # Convert from Kociemba format to original format for processing
            original_cube_string = convert_from_kociemba_format(cube_string)
            print(f"Converted from Kociemba format: {cube_string} -> {original_cube_string}")
        else:
            original_cube_string = cube_string

        print(f"Solving cube with {cube_size} stickers")

        # Route to appropriate solver based on cube size
        if cube_size == 24:  # 2x2 cube
            return solve_2x2_cube(original_cube_string)
        elif cube_size == 54:  # 3x3 cube
            return solve_3x3_cube(original_cube_string)
        elif cube_size == 96:  # 4x4 cube
            return solve_4x4_cube(original_cube_string)
        else:
            return jsonify({
                'error': f'Unsupported cube size: {cube_size} stickers',
                'supported_sizes': '24 (2x2), 54 (3x3), 96 (4x4)'
            }), 400

    except Exception as e:
        print(f"Server error: {str(e)}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

def solve_2x2_cube(cube_string):
    """Solve 2x2 cube using cube-solver library"""
    if not CUBE_SOLVER_AVAILABLE:
        return jsonify({
            'error': '2x2 solver not available',
            'note': 'Install cube-solver: pip install cube-solver'
        }), 501

    try:
        print(f"Solving 2x2 cube: {cube_string}")

        # Convert our format to the expected format for cube-solver
        # Our format: faces in order U,R,F,D,L,B with colors WYGBRO
        # cube-solver expects: UUUURRRRFFFFDDDDLLLLBBBB format

        solution = solve_222(cube_string)

        if not solution:
            return jsonify({'error': 'No solution found for 2x2 cube'}), 400

        # Parse solution into individual moves
        moves = solution.split() if solution else []

        return jsonify({
            'solution': solution,
            'moves': moves,
            'move_count': len(moves),
            'cube_string': cube_string,
            'solver': '2x2 cube-solver'
        })

    except Exception as e:
        print(f"2x2 solver error: {str(e)}")
        return jsonify({'error': f'2x2 solver error: {str(e)}'}), 500

def solve_3x3_cube(cube_string):
    """Solve 3x3 cube using Kociemba algorithm"""
    try:
        print(f"Solving 3x3 cube: {cube_string}")

        # Ensure we have original format for conversion
        format_type = detect_cube_format(cube_string)
        if format_type == "kociemba":
            # Already in Kociemba format, use directly
            kociemba_string = cube_string
            original_string = convert_from_kociemba_format(cube_string)
        else:
            # Convert from original format to Kociemba format
            kociemba_string = convert_to_kociemba_format(cube_string)
            original_string = cube_string

        print(f"Kociemba format: {kociemba_string}")

        # Solve using Kociemba algorithm
        solution = kociemba.solve(kociemba_string)

        if solution == "Error":
            return jsonify({'error': 'Cube configuration is unsolvable'}), 400

        return jsonify({
            'solution': solution,
            'moves': solution.split(),
            'move_count': len(solution.split()),
            'cube_string': original_string,
            'kociemba_string': kociemba_string,
            'solver': 'Kociemba 3x3'
        })

    except Exception as e:
        print(f"3x3 solver error: {str(e)}")
        return jsonify({'error': f'3x3 solver error: {str(e)}'}), 500

def solve_4x4_cube(cube_string):
    """Solve 4x4 cube using rubik-solver library"""
    if not RUBIK_SOLVER_AVAILABLE:
        return jsonify({
            'error': '4x4 solver not available',
            'note': 'Install rubik-solver: pip install rubik-solver'
        }), 501

    try:
        print(f"Solving 4x4 cube: {cube_string}")

        # For now, return a placeholder since 4x4 solving is complex
        # The rubik-solver library works with scrambles, not cube states
        return jsonify({
            'error': '4x4 solving from cube state not yet implemented',
            'note': 'Use scramble generation and solving instead',
            'cube_string': cube_string,
            'solver': '4x4 rubik-solver (placeholder)'
        }), 501

    except Exception as e:
        print(f"4x4 solver error: {str(e)}")
        return jsonify({'error': f'4x4 solver error: {str(e)}'}), 500

@app.route('/info', methods=['GET'])
def get_info():
    """Get server information"""
    return jsonify({
        'name': 'Clean Rubik\'s Cube Solver Server',
        'version': '1.0.0',
        'supported_sizes': ['2x2', '3x3', '4x4'],
        'solver_algorithm': 'Kociemba Two-Phase Algorithm',
        'solver_support': '3x3 cubes only',
        'color_format': 'WYGBRO (White, Yellow, Green, Blue, Red, Orange)',
        'endpoints': {
            '/health': 'Health check',
            '/validate': 'Validate cube string',
            '/solve': 'Solve 3x3 cube',
            '/info': 'Server information'
        }
    })

if __name__ == '__main__':
    print("🎲 Starting Clean Rubik's Cube Solver Server...")
    print("📡 Server will run on http://localhost:5000")
    print("🔧 Supported cube sizes: 2x2, 3x3, 4x4")
    print("🧩 Kociemba solver available for 3x3 cubes")
    print("🌐 CORS enabled for browser access")
    
    app.run(debug=True, host='localhost', port=5000)
