<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Clean Cube Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f0f0;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 10px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>🎲 Clean Rubik's Cube Implementation Test</h1>
    
    <div class="test-section">
        <h2>1. Component Loading Test</h2>
        <button onclick="testComponentLoading()">Test Component Loading</button>
        <div id="component-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. Cube State Test</h2>
        <button onclick="testCubeState()">Test Cube State</button>
        <div id="cube-state-result"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Server Connection Test</h2>
        <button onclick="testServerConnection()">Test Server Connection</button>
        <div id="server-test-result"></div>
    </div>
    
    <div class="test-section">
        <h2>4. Face Move Test</h2>
        <button onclick="testFaceMoves()">Test Face Moves</button>
        <div id="face-move-result"></div>
    </div>
    
    <div class="test-section">
        <h2>5. Scramble Test</h2>
        <button onclick="testScramble()">Test Scramble</button>
        <div id="scramble-result"></div>
    </div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="js/cube-state.js"></script>
    <script src="js/cube-3d.js"></script>
    <script src="js/cube-2d.js"></script>
    <script src="js/controls.js"></script>
    <script src="js/solver.js"></script>
    <script src="js/app.js"></script>
    
    <script>
        function showResult(elementId, success, message, details = null) {
            const element = document.getElementById(elementId);
            const className = success ? 'success' : 'error';
            let html = `<div class="test-result ${className}">
                <strong>${success ? '✅ SUCCESS' : '❌ ERROR'}:</strong> ${message}
            </div>`;
            
            if (details) {
                html += `<pre>${JSON.stringify(details, null, 2)}</pre>`;
            }
            
            element.innerHTML = html;
        }
        
        function testComponentLoading() {
            try {
                const components = {
                    'THREE': typeof THREE !== 'undefined',
                    'CubeState': typeof CubeState !== 'undefined',
                    'Cube3D': typeof Cube3D !== 'undefined',
                    'Cube2D': typeof Cube2D !== 'undefined',
                    'Controls': typeof Controls !== 'undefined',
                    'Solver': typeof Solver !== 'undefined',
                    'App': typeof App !== 'undefined'
                };
                
                const allLoaded = Object.values(components).every(loaded => loaded);
                
                showResult('component-test-result', allLoaded, 
                    allLoaded ? 'All components loaded successfully' : 'Some components failed to load',
                    components);
                    
            } catch (error) {
                showResult('component-test-result', false, 'Component loading test failed', error.message);
            }
        }
        
        function testCubeState() {
            try {
                const cubeState = new CubeState(3);
                const initialString = cubeState.cubeString;
                
                // Test face move
                const newString = cubeState.executeMove('F');
                const moveWorked = newString !== initialString;
                
                // Test reset
                cubeState.reset();
                const resetWorked = cubeState.cubeString === initialString;
                
                const results = {
                    initialString: initialString,
                    afterMove: newString,
                    afterReset: cubeState.cubeString,
                    moveWorked: moveWorked,
                    resetWorked: resetWorked
                };
                
                const success = moveWorked && resetWorked;
                showResult('cube-state-result', success, 
                    success ? 'Cube state working correctly' : 'Cube state has issues',
                    results);
                    
            } catch (error) {
                showResult('cube-state-result', false, 'Cube state test failed', error.message);
            }
        }
        
        async function testServerConnection() {
            try {
                const solver = new Solver();
                
                // Test health endpoint
                const response = await fetch('http://localhost:5000/health');
                const data = await response.json();
                
                showResult('server-test-result', response.ok, 
                    response.ok ? 'Server connection successful' : 'Server connection failed',
                    data);
                    
            } catch (error) {
                showResult('server-test-result', false, 'Server connection test failed', error.message);
            }
        }
        
        function testFaceMoves() {
            try {
                const cubeState = new CubeState(3);
                const initialString = cubeState.cubeString;
                
                // Test all face moves
                const moves = ['F', 'R', 'U', 'B', 'L', 'D'];
                const results = {};
                
                for (const move of moves) {
                    cubeState.reset();
                    const before = cubeState.cubeString;
                    const after = cubeState.executeMove(move);
                    results[move] = {
                        changed: before !== after,
                        before: before.substring(0, 20) + '...',
                        after: after.substring(0, 20) + '...'
                    };
                }
                
                const allWorking = Object.values(results).every(r => r.changed);
                
                showResult('face-move-result', allWorking,
                    allWorking ? 'All face moves working' : 'Some face moves not working',
                    results);
                    
            } catch (error) {
                showResult('face-move-result', false, 'Face move test failed', error.message);
            }
        }
        
        function testScramble() {
            try {
                const cubeState = new CubeState(3);
                const initialString = cubeState.cubeString;
                
                // Test scramble
                const scrambleMoves = cubeState.generateScramble(10);
                cubeState.applyMoves(scrambleMoves);
                const scrambledString = cubeState.cubeString;
                
                const results = {
                    scrambleMoves: scrambleMoves,
                    initialString: initialString.substring(0, 20) + '...',
                    scrambledString: scrambledString.substring(0, 20) + '...',
                    changed: initialString !== scrambledString
                };
                
                showResult('scramble-result', results.changed,
                    results.changed ? 'Scramble working correctly' : 'Scramble not working',
                    results);
                    
            } catch (error) {
                showResult('scramble-result', false, 'Scramble test failed', error.message);
            }
        }
        
        // Auto-run component loading test
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(testComponentLoading, 500);
        });
    </script>
</body>
</html>
