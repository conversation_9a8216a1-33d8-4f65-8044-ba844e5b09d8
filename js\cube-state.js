/**
 * Clean Cube State Management
 * Handles the logical state of the Rubik's cube using string representation
 * Independent of visual orientation
 */

class CubeState {
    constructor(size = 3) {
        this.size = size;
        this.initializeState();
    }

    initializeState() {
        // Color mappings
        this.colors = {
            'U': 'white',   // Up face
            'D': 'yellow',  // Down face  
            'F': 'green',   // Front face
            'B': 'blue',    // Back face
            'R': 'red',     // Right face
            'L': 'orange'   // Left face
        };

        this.colorToChar = {
            'white': 'W',
            'yellow': 'Y',
            'green': 'G', 
            'blue': 'B',
            'red': 'R',
            'orange': 'O'
        };

        this.charToColor = {
            'W': 'white',
            'Y': 'yellow',
            'G': 'green',
            'B': 'blue', 
            'R': 'red',
            'O': 'orange'
        };

        // Initialize solved cube string
        this.resetToSolved();
    }

    resetToSolved() {
        const stickersPerFace = this.size * this.size;
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        
        this.cubeString = '';
        faceOrder.forEach(face => {
            const color = this.colors[face];
            const char = this.colorToChar[color];
            this.cubeString += char.repeat(stickersPerFace);
        });

        console.log(`Initialized ${this.size}x${this.size} cube:`, this.cubeString);
    }

    // Convert string to face dictionary for easier manipulation
    stringToFaces() {
        const faces = {};
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        const stickersPerFace = this.size * this.size;

        faceOrder.forEach((face, faceIndex) => {
            faces[face] = [];
            for (let row = 0; row < this.size; row++) {
                faces[face][row] = [];
                for (let col = 0; col < this.size; col++) {
                    const stringIndex = faceIndex * stickersPerFace + row * this.size + col;
                    const char = this.cubeString[stringIndex];
                    const color = this.charToColor[char] || 'gray';
                    faces[face][row][col] = color;
                }
            }
        });

        return faces;
    }

    // Convert face dictionary back to string
    facesToString(faces) {
        const faceOrder = ['U', 'R', 'F', 'D', 'L', 'B'];
        let result = '';

        faceOrder.forEach(face => {
            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    const color = faces[face][row][col];
                    const char = this.colorToChar[color] || 'W';
                    result += char;
                }
            }
        });

        return result;
    }

    // Rotate a face array 90 degrees
    rotateFaceArray(faceArray, clockwise = true) {
        const size = this.size;
        const temp = faceArray.map(row => [...row]);

        if (clockwise) {
            // Clockwise: (i,j) -> (j, size-1-i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    faceArray[j][size - 1 - i] = temp[i][j];
                }
            }
        } else {
            // Counterclockwise: (i,j) -> (size-1-j, i)
            for (let i = 0; i < size; i++) {
                for (let j = 0; j < size; j++) {
                    faceArray[size - 1 - j][i] = temp[i][j];
                }
            }
        }
    }

    // Get column from face array
    getColumn(faceArray, colIndex) {
        return faceArray.map(row => row[colIndex]);
    }

    // Set column in face array
    setColumn(faceArray, colIndex, values) {
        for (let i = 0; i < faceArray.length; i++) {
            faceArray[i][colIndex] = values[i];
        }
    }

    // Validate cube state integrity
    validateCubeState() {
        const counts = {};
        for (let char of this.cubeString) {
            counts[char] = (counts[char] || 0) + 1;
        }
        
        // Check if each color appears exactly 9 times
        const isValid = ['W', 'Y', 'G', 'B', 'R', 'O'].every(color => counts[color] === 9);
        
        if (!isValid) {
            console.error('Invalid cube state detected:', counts);
            return false;
        }
        return true;
    }

    // Execute a face rotation move
    executeMove(face, clockwise = true) {
        console.log(`Executing move: ${face}${clockwise ? '' : "'"}`);

        const faces = this.stringToFaces();
        
        // Rotate the face itself
        this.rotateFaceArray(faces[face], clockwise);
        
        // Update adjacent faces based on the specific face being rotated
        this.updateAdjacentFaces(faces, face, clockwise);
        
        // Convert back to string
        this.cubeString = this.facesToString(faces);
        
        // Validate the new state
        if (!this.validateCubeState()) {
            console.error('Move resulted in invalid cube state:', face, clockwise);
            this.resetToSolved(); // Reset to prevent invalid state
            throw new Error('Invalid cube state detected after move');
        }
        
        console.log('Move completed, new string:', this.cubeString.substring(0, 20) + '...');
        return this.cubeString;
    }

    executeWideMove(wideFace, clockwise = true) {
        // Wide moves for 4x4 cubes (e.g., Rw, Uw, etc.)
        // A wide move affects the outer face and the adjacent inner layer

        if (this.size !== 4) {
            console.warn('Wide moves are only supported for 4x4 cubes');
            return this.cubeString;
        }

        const baseFace = wideFace.replace('w', ''); // Remove 'w' to get base face
        console.log(`Executing wide move: ${wideFace}${clockwise ? '' : "'"} (base: ${baseFace})`);

        // For now, implement wide moves as two separate moves:
        // 1. The outer layer (regular move)
        // 2. The inner layer (slice move)

        // Execute the outer layer move
        this.executeMove(baseFace, clockwise);

        // Execute the inner layer move (simplified implementation)
        // For a proper implementation, we would need slice move logic
        // For now, we'll just do the outer move

        console.log('Wide move completed (simplified implementation)');
        return this.cubeString;
    }

    // Update adjacent faces when a face is rotated
    updateAdjacentFaces(faces, face, clockwise) {
        const size = this.size;
        
        // Define adjacent face relationships for each face rotation
        const adjacentMappings = {
            'U': {
                faces: ['B', 'R', 'F', 'L'],
                getters: [
                    () => faces.B[0].slice(),
                    () => faces.R[0].slice(), 
                    () => faces.F[0].slice(),
                    () => faces.L[0].slice()
                ],
                setters: [
                    (vals) => faces.B[0] = vals.slice(),
                    (vals) => faces.R[0] = vals.slice(),
                    (vals) => faces.F[0] = vals.slice(),
                    (vals) => faces.L[0] = vals.slice()
                ]
            },
            'D': {
                faces: ['F', 'R', 'B', 'L'],
                getters: [
                    () => faces.F[size-1].slice(),
                    () => faces.R[size-1].slice(),
                    () => faces.B[size-1].slice(),
                    () => faces.L[size-1].slice()
                ],
                setters: [
                    (vals) => faces.F[size-1] = vals.slice(),
                    (vals) => faces.R[size-1] = vals.slice(),
                    (vals) => faces.B[size-1] = vals.slice(),
                    (vals) => faces.L[size-1] = vals.slice()
                ]
            },
            'F': {
                faces: ['U', 'R', 'D', 'L'],
                getters: [
                    () => faces.U[size-1].slice(),
                    () => this.getColumn(faces.R, 0).slice(),
                    () => faces.D[0].slice(),
                    () => this.getColumn(faces.L, size-1).slice()
                ],
                setters: [
                    (vals) => faces.U[size-1] = vals,
                    (vals) => this.setColumn(faces.R, 0, vals),
                    (vals) => faces.D[0] = vals.reverse(),
                    (vals) => this.setColumn(faces.L, size-1, vals.reverse())
                ]
            },
            'B': {
                faces: ['U', 'L', 'D', 'R'],
                getters: [
                    () => faces.U[0].slice(),
                    () => this.getColumn(faces.L, 0).slice(),
                    () => faces.D[size-1].slice(),
                    () => this.getColumn(faces.R, size-1).slice()
                ],
                setters: [
                    (vals) => faces.U[0] = vals.reverse(),
                    (vals) => this.setColumn(faces.L, 0, vals),
                    (vals) => faces.D[size-1] = vals.reverse(),
                    (vals) => this.setColumn(faces.R, size-1, vals)
                ]
            },
            'R': {
                faces: ['U', 'B', 'D', 'F'],
                getters: [
                    () => this.getColumn(faces.U, size-1).slice(),
                    () => this.getColumn(faces.B, 0).slice(),
                    () => this.getColumn(faces.D, size-1).slice(),
                    () => this.getColumn(faces.F, size-1).slice()
                ],
                setters: [
                    (vals) => this.setColumn(faces.U, size-1, vals),
                    (vals) => this.setColumn(faces.B, 0, vals.reverse()),
                    (vals) => this.setColumn(faces.D, size-1, vals),
                    (vals) => this.setColumn(faces.F, size-1, vals)
                ]
            },
            'L': {
                faces: ['U', 'F', 'D', 'B'],
                getters: [
                    () => this.getColumn(faces.U, 0).slice(),
                    () => this.getColumn(faces.F, 0).slice(),
                    () => this.getColumn(faces.D, 0).slice(),
                    () => this.getColumn(faces.B, size-1).slice()
                ],
                setters: [
                    (vals) => this.setColumn(faces.U, 0, vals),
                    (vals) => this.setColumn(faces.F, 0, vals),
                    (vals) => this.setColumn(faces.D, 0, vals),
                    (vals) => this.setColumn(faces.B, size-1, vals.reverse())
                ]
            }
        };

        const mapping = adjacentMappings[face];
        if (!mapping) return;

        // Get current edge values
        const edges = mapping.getters.map(getter => getter());
        
        // Rotate edges
        if (clockwise) {
            // Move edges clockwise: 0->1, 1->2, 2->3, 3->0
            const temp = edges[3];
            for (let i = 3; i > 0; i--) {
                edges[i] = edges[i-1];
            }
            edges[0] = temp;
        } else {
            // Move edges counterclockwise: 0->3, 1->0, 2->1, 3->2
            const temp = edges[0];
            for (let i = 0; i < 3; i++) {
                edges[i] = edges[i+1];
            }
            edges[3] = temp;
        }
        
        // Set new edge values
        mapping.setters.forEach((setter, i) => setter(edges[i]));
    }

    // Generate random scramble
    generateScramble(moves = 20) {
        const faces = ['U', 'D', 'F', 'B', 'R', 'L'];
        const scramble = [];

        for (let i = 0; i < moves; i++) {
            const face = faces[Math.floor(Math.random() * faces.length)];
            const clockwise = Math.random() > 0.5;
            scramble.push(face + (clockwise ? '' : "'"));
        }

        return scramble.join(' ');
    }

    // Apply scramble sequence
    applyScramble(scrambleString) {
        const moves = scrambleString.split(' ').filter(move => move.trim());

        moves.forEach(move => {
            const face = move[0];
            const clockwise = !move.includes("'");
            this.executeMove(face, clockwise);
        });

        return this.cubeString;
    }

    // Apply solution sequence
    applySolution(solutionString) {
        return this.applyScramble(solutionString); // Same logic as scramble
    }

    // Check if cube is solved
    isSolved() {
        const faces = this.stringToFaces();

        for (const face of ['U', 'R', 'F', 'D', 'L', 'B']) {
            const faceArray = faces[face];
            const firstColor = faceArray[0][0];

            for (let row = 0; row < this.size; row++) {
                for (let col = 0; col < this.size; col++) {
                    if (faceArray[row][col] !== firstColor) {
                        return false;
                    }
                }
            }
        }

        return true;
    }

    // Change cube size
    changeSize(newSize) {
        if (![2, 3, 4].includes(newSize)) {
            console.error('Invalid cube size:', newSize);
            return;
        }

        this.size = newSize;
        this.resetToSolved();
        return this.cubeString;
    }

    // Get cube state as Kociemba format (for 3x3 only)
    getKociembaString() {
        if (this.size !== 3) {
            console.warn('Kociemba format only available for 3x3 cubes');
            return null;
        }
        const mapping = {
            'W': 'U',  // White -> Up
            'Y': 'D',  // Yellow -> Down
            'G': 'F',  // Green -> Front
            'B': 'B',  // Blue -> Back
            'R': 'R',  // Red -> Right
            'O': 'L'   // Orange -> Left
        };
        
        return this.cubeString.split('').map(c => mapping[c]).join('');
    }
}

// Export for use in other modules
window.CubeState = CubeState;
