/**
 * Clean Application Main
 * Initializes and coordinates all components
 */

class App {
    constructor() {
        this.cubeState = null;
        this.cube3D = null;
        this.cube2D = null;
        this.controls = null;
        this.solver = null;
        
        this.init();
    }

    async init() {
        try {
            console.log('Initializing Clean Rubik\'s Cube Solver...');
            
            // Initialize cube state (default 3x3)
            this.cubeState = new CubeState(3);
            console.log('✓ Cube state initialized');
            
            // Initialize 3D visualization
            const canvas = document.getElementById('cube-canvas');
            if (!canvas) {
                throw new Error('Canvas element not found');
            }
            this.cube3D = new Cube3D(canvas, this.cubeState);
            console.log('✓ 3D visualization initialized');
            
            // Initialize 2D visualization
            const flatCube = document.getElementById('flat-cube');
            if (!flatCube) {
                throw new Error('Flat cube container not found');
            }
            this.cube2D = new Cube2D(flatCube, this.cubeState);
            console.log('✓ 2D visualization initialized');
            
            // Initialize controls
            this.controls = new Controls(this.cubeState, this.cube3D, this.cube2D);
            console.log('✓ Controls initialized');
            
            // Initialize solver
            this.solver = new Solver();
            console.log('✓ Solver initialized');
            
            // Initial display update
            this.updateDisplay();
            
            console.log('🎉 Application initialized successfully!');
            
        } catch (error) {
            console.error('❌ Application initialization failed:', error);
            this.showError('Failed to initialize application: ' + error.message);
        }
    }

    updateDisplay() {
        // Update cube string display
        const stringDisplay = document.getElementById('cube-string-display');
        if (stringDisplay && this.cubeState) {
            stringDisplay.textContent = this.cubeState.cubeString;
        }
    }

    onStateChange() {
        // Called when cube state changes
        this.updateDisplay();
        
        // Update visualizations
        if (this.cube3D) this.cube3D.refresh();
        if (this.cube2D) this.cube2D.refresh();
    }

    showError(message) {
        // Show error message to user
        const errorDiv = document.createElement('div');
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #f44336;
            color: white;
            padding: 15px;
            border-radius: 5px;
            z-index: 1000;
            max-width: 300px;
        `;
        errorDiv.textContent = message;
        
        document.body.appendChild(errorDiv);
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    // Public API methods
    executeMove(face, clockwise = true) {
        if (this.controls) {
            this.controls.executeFaceMove(face, clockwise);
        }
    }

    scramble() {
        if (this.controls) {
            this.controls.autoScramble();
        }
    }

    solve() {
        if (this.controls) {
            this.controls.solveCube();
        }
    }

    reset() {
        if (this.controls) {
            this.controls.resetCube();
        }
    }

    changeSize(size) {
        if (this.controls) {
            this.controls.changeSize(size);
        }
    }
}

// Initialize application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new App();
});

// Export for global access
window.App = App;
